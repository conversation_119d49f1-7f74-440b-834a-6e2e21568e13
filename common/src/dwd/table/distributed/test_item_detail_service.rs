use std::collections::HashMap;
use std::error::Error;

use crate::dto::dwd::sub_test_item_detail::SubTestItemDetail;
use crate::dto::ods::test_item_data_parquet::TestItemDataParquet;
use crate::dwd::model::value::die_test_info::DieTestInfo;
use crate::dwd::table::test_item_detail_common_service::TestItemDetailCommonService;
use crate::model::constant::test_state::TestState;
use crate::model::key::die_key::Die<PERSON>ey;

// Corresponding to Scala file:
// /dataware/dataware-dw/dataware-dw-common/src/main/scala/com/guwave/onedata/dataware/dw/common/dwd/table/distributed/TestItemDetailService.scala

/// TestItemDetailService handles distributed processing of test item details
/// Extends TestItemDetailCommonService with distributed-specific functionality
#[derive(Debug, Clone)]
pub struct TestItemDetailService {
    test_area: String,
    common_service: TestItemDetailCommonService,
}

impl TestItemDetailService {
    /// Create new TestItemDetailService with test area
    ///
    /// Corresponds to: TestItemDetailService.scala:30 (constructor)
    pub fn new(test_area: String) -> Self {
        Self { test_area, common_service: TestItemDetailCommonService::new() }
    }

    /// Calculate CP test item details with chunked processing for better memory management
    ///
    /// Corresponds to: TestItemDetailService.scala:43-62
    /// def calculateCpTestItemDetail(spark: SparkSession, sourceTestItemData: Dataset[TestItemData],
    ///                              fileCategory: String, dieTestInfoMap: Broadcast[Map[DieKey, DieTestInfo]],
    ///                              needMultiplyScale: Boolean, standardUnits: String, needClearInvalidData: Boolean): Dataset[SubTestItemDetail]
    pub async fn calculate_cp_test_item_detail(
        &self,
        source_test_item_data: Vec<TestItemDataParquet>,
        file_category: &str,
        die_test_info_map: &HashMap<DieKey, DieTestInfo>,
        need_multiply_scale: bool,
        standard_units: &str,
        need_clear_invalid_data: bool,
        test_num_force_zero_test_program_list: &[String],
    ) -> Result<Vec<SubTestItemDetail>, Box<dyn Error>> {
        // 使用分块处理以优化内存使用 - Use chunked processing for memory optimization
        self.calculate_cp_test_item_detail_chunked(
            source_test_item_data,
            file_category,
            die_test_info_map,
            need_multiply_scale,
            standard_units,
            need_clear_invalid_data,
            test_num_force_zero_test_program_list,
            100000, // 默认块大小 - Default chunk size
        ).await
    }

    /// Calculate CP test item details with configurable chunk size
    pub async fn calculate_cp_test_item_detail_chunked(
        &self,
        source_test_item_data: Vec<TestItemDataParquet>,
        file_category: &str,
        die_test_info_map: &HashMap<DieKey, DieTestInfo>,
        need_multiply_scale: bool,
        standard_units: &str,
        need_clear_invalid_data: bool,
        test_num_force_zero_test_program_list: &[String],
        chunk_size: usize,
    ) -> Result<Vec<SubTestItemDetail>, Box<dyn Error>> {
        // Create standard units map (corresponds to line 52 in Scala)
        let standard_units_map = self.common_service.get_standard_units(need_multiply_scale, standard_units)?;

        // 分块处理数据以优化内存使用 - Chunked processing for memory optimization
        let mut all_results = Vec::new();

        // 按块处理数据 - Process data in chunks
        for chunk in source_test_item_data.chunks(chunk_size) {
            let chunk_result: Result<Vec<SubTestItemDetail>, Box<dyn Error>> = chunk
                .iter()  // 使用普通迭代器避免Send问题 - Use regular iterator to avoid Send issues
                .filter(|item| {
                    let die_key = DieKey::new(item.fileId.unwrap_or(0), item.cPartId.unwrap_or(0) as i64);
                    die_test_info_map.contains_key(&die_key)
                })
                .map(|item| {
                    self.common_service
                        .build_cp_test_item_detail(item, &standard_units_map, file_category, need_multiply_scale)
                })
                .collect::<Result<Vec<_>, _>>()?
                .into_iter()
                .filter(|sub_test_item_detail| {
                    // Filter invalid data if needClearInvalidData is true (corresponds to line 59 in Scala)
                    if need_clear_invalid_data {
                        sub_test_item_detail.TEST_STATE == Some(TestState::Valid.to_string())
                    } else {
                        true
                    }
                })
                .map(|sub_test_item_detail| {
                    self.common_service.fill_die_test_info(
                        sub_test_item_detail,
                        die_test_info_map,
                        test_num_force_zero_test_program_list,
                    )
                })
                .collect();

            all_results.extend(chunk_result?);
        }

        Ok(all_results)
    }

    /// Calculate FT test item details
    ///
    /// Corresponds to: TestItemDetailService.scala:74-90
    /// def calculateFtTestItemDetail(spark: SparkSession, sourceTestItemData: Dataset[TestItemData],
    ///                              fileCategory: String, dieTestInfoMap: Broadcast[Map[DieKey, DieTestInfo]],
    ///                              needMultiplyScale: Boolean, standardUnits: String, needClearInvalidData: Boolean): Dataset[SubTestItemDetail]
    pub async fn calculate_ft_test_item_detail(
        &self,
        source_test_item_data: Vec<TestItemDataParquet>,
        file_category: &str,
        die_test_info_map: &HashMap<DieKey, DieTestInfo>,
        need_multiply_scale: bool,
        standard_units: &str,
        need_clear_invalid_data: bool,
    ) -> Result<Vec<SubTestItemDetail>, Box<dyn Error>> {
        // 使用分块处理以优化内存使用 - Use chunked processing for memory optimization
        self.calculate_ft_test_item_detail_chunked(
            source_test_item_data,
            file_category,
            die_test_info_map,
            need_multiply_scale,
            standard_units,
            need_clear_invalid_data,
            10000, // 默认块大小 - Default chunk size
        ).await
    }

    /// Calculate FT test item details with configurable chunk size
    pub async fn calculate_ft_test_item_detail_chunked(
        &self,
        source_test_item_data: Vec<TestItemDataParquet>,
        file_category: &str,
        die_test_info_map: &HashMap<DieKey, DieTestInfo>,
        need_multiply_scale: bool,
        standard_units: &str,
        need_clear_invalid_data: bool,
        chunk_size: usize,
    ) -> Result<Vec<SubTestItemDetail>, Box<dyn Error>> {
        let standard_units_map = self.common_service.get_standard_units(need_multiply_scale, standard_units)?;

        // 分块处理FT数据 - Chunked FT data processing
        let mut all_results = Vec::new();

        // 按块处理数据 - Process data in chunks
        for chunk in source_test_item_data.chunks(chunk_size) {
            let chunk_result: Vec<SubTestItemDetail> = chunk
                .iter()  // 使用普通迭代器避免Send问题 - Use regular iterator to avoid Send issues
                // 去重
                .filter(|item| {
                    let die_key = DieKey::new(item.fileId.unwrap_or(0), item.cPartId.unwrap_or(0) as i64);
                    die_test_info_map.contains_key(&die_key)
                })
                // 构建TestItemDetail
                .map(|item| {
                    self.common_service
                        .build_ft_test_item_detail(item, &standard_units_map, file_category, need_multiply_scale)
                        .unwrap()
                })
                .filter(|sub_test_item_detail| {
                    if need_clear_invalid_data {
                        sub_test_item_detail.TEST_STATE == Some(TestState::Valid.to_string())
                    } else {
                        true
                    }
                })
                .collect::<Vec<SubTestItemDetail>>();

            all_results.extend(chunk_result);
        }

        Ok(all_results)
    }
}
