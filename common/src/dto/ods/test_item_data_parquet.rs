use crate::parquet::RecordBatchWrapper;
use arrow::datatypes::FieldRef;
use arrow::record_batch::RecordBatch;
use serde::{Deserialize, Serialize};
use serde_arrow::schema::{SchemaLike, TracingOptions};
use std::collections::HashMap;

/// TestItemData struct for reading from parquet files
/// This matches the actual parquet schema with camelCase field names
#[derive(Serialize, Deserialize, Debug, PartialEq)]
#[allow(non_snake_case)]
pub struct TestItemDataParquet {
    pub alarmId: Option<String>,
    pub cHlmfmt: Option<String>,
    pub cLlmfmt: Option<String>,
    pub cPartId: Option<i32>,
    pub cResfmt: Option<String>,
    pub conditionSet: Option<HashMap<String, String>>,
    pub cyclCnt: Option<i64>,
    pub ecid: Option<String>,
    pub ecidExt: Option<String>,
    pub ecidExtra: Option<HashMap<String, String>>,
    pub failPin: Option<String>,
    pub fileId: Option<i64>,
    pub fileName: Option<String>,
    pub hbinNam: Option<String>,
    pub hbinNum: Option<i64>,
    pub hbinPf: Option<String>,
    pub hiLimit: Option<f64>,
    pub hiSpec: Option<f64>,
    pub hlmScal: Option<i32>,
    pub llmScal: Option<i32>,
    pub loLimit: Option<f64>,
    pub loSpec: Option<f64>,
    pub maxOnlineRetest: Option<i32>,
    pub numFail: Option<i64>,
    pub numTest: Option<i32>,
    pub onlineRetest: Option<i32>,
    pub optFlg: Option<String>,
    pub parmFlg: Option<String>,
    pub partFix: Option<String>,
    pub partFlg: Option<String>,
    pub partId: Option<String>,
    pub partTxt: Option<String>,
    pub realWaferId: Option<String>,
    pub reptCnt: Option<i64>,
    pub resScal: Option<i32>,
    pub result: Option<f64>,
    pub sbinNam: Option<String>,
    pub sbinNum: Option<i64>,
    pub sbinPf: Option<String>,
    pub site: Option<i64>,
    pub testFlg: Option<String>,
    pub testHead: Option<i64>,
    pub testItem: Option<String>,
    pub testNum: Option<i64>,
    pub testOrder: Option<i64>,
    pub testResult: Option<i32>,
    pub testTime: Option<i64>,
    pub testTxt: Option<String>,
    pub testValue: Option<f64>,
    pub testitemType: Option<String>,
    pub textDat: Option<String>,
    pub timeSet: Option<String>,
    pub touchDownId: Option<i32>,
    pub uid: Option<String>,
    pub units: Option<String>,
    pub vectNam: Option<String>,
    pub waferId: Option<String>,
    pub waferLotId: Option<String>,
    pub waferNo: Option<String>,
    pub xCoord: Option<i32>,
    pub yCoord: Option<i32>,
    pub efuseExtra: Option<HashMap<String, String>>,
    pub chipId: Option<String>,
}

impl RecordBatchWrapper for TestItemDataParquet {
    fn from_record_batch(batch: &RecordBatch) -> Result<Vec<Self>, String>
    where
        Self: Sized,
    {
        let result: Vec<TestItemDataParquet> = serde_arrow::from_record_batch(batch).unwrap();
        Ok(result)
    }

    fn to_record_batch(data: &Vec<Self>) -> Result<RecordBatch, String>
    where
        Self: Sized,
    {
        let fields = Vec::<FieldRef>::from_type::<TestItemDataParquet>(TracingOptions::default().allow_null_fields(true).map_as_struct(false)).unwrap();
        let record_batch = serde_arrow::to_record_batch(&fields, data).unwrap();
        Ok(record_batch)
    }
}
