use chrono::Utc;
use common::dto::dwd::sub_test_item_detail::SubTestItemDetail;
use parquet_provider::hdfs_provider::HdfsConfig;
use parquet_provider::parquet_provider::write_parquet;
use std::collections::HashMap;
use tracing::Level;
use tracing_subscriber::fmt::format::FmtSpan;

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    // 设置日志
    tracing_subscriber::fmt()
        .with_max_level(Level::INFO)
        .with_span_events(FmtSpan::FULL)
        .with_file(true)
        .with_line_number(true)
        .init();

    // 创建测试数据
    let test_data = create_test_data(1500); // 创建1500条记录用于测试chunk功能

    // 测试chunk写入功能
    test_chunk_parquet_write(&test_data).await?;

    Ok(())
}

fn create_test_data(count: usize) -> Vec<SubTestItemDetail> {
    let mut data = Vec::new();
    
    for i in 0..count {
        let mut condition_set = HashMap::new();
        condition_set.insert("TEMP".to_string(), "25C".to_string());

        let mut ecid_extra = HashMap::new();
        ecid_extra.insert("ECID_KEY1".to_string(), format!("ECID_VALUE_{}", i));

        let mut long_attribute_set = HashMap::new();
        long_attribute_set.insert("LONG_ATTR1".to_string(), (12345 + i) as i64);

        let mut string_attribute_set = HashMap::new();
        string_attribute_set.insert("STRING_ATTR1".to_string(), format!("STRING_VALUE_{}", i));

        let mut float_attribute_set = HashMap::new();
        float_attribute_set.insert("FLOAT_ATTR1".to_string(), 3.14 + i as f64);

        let mut efuse_extra = HashMap::new();
        efuse_extra.insert("EFUSE_KEY1".to_string(), format!("EFUSE_VALUE_{}", i));

        data.push(SubTestItemDetail {
            FILE_ID: Some(12345 + i as i64),
            ONLINE_RETEST: Some(0),
            MAX_OFFLINE_RETEST: Some(0),
            MAX_ONLINE_RETEST: Some(0),
            IS_DIE_FIRST_TEST: Some(1),
            IS_DIE_FINAL_TEST: Some(1),
            IS_FIRST_TEST: Some(1),
            IS_FINAL_TEST: Some(1),
            IS_FIRST_TEST_IGNORE_TP: Some(1),
            IS_FINAL_TEST_IGNORE_TP: Some(1),
            IS_DUP_FIRST_TEST: Some(0),
            IS_DUP_FINAL_TEST: Some(0),
            IS_DUP_FIRST_TEST_IGNORE_TP: Some(0),
            IS_DUP_FINAL_TEST_IGNORE_TP: Some(0),
            TEST_SUITE: Some(format!("TEST_SUITE_{}", i)),
            CONDITION_SET: Some(condition_set),
            TEST_NUM: Some(1000 + i as i64),
            TEST_TXT: Some(format!("TEST_TXT_{}", i)),
            TEST_ITEM: Some(format!("TEST_ITEM_{}", i)),
            IS_DIE_FIRST_TEST_ITEM: Some(1),
            TESTITEM_TYPE: Some("FUNCTIONAL".to_string()),
            TEST_FLG: Some("P".to_string()),
            PARM_FLG: Some("N".to_string()),
            TEST_STATE: Some("Valid".to_string()),
            TEST_VALUE: Some(1.23 + i as f64),
            UNITS: Some("V".to_string()),
            TEST_RESULT: Some(1),
            ORIGIN_TEST_VALUE: Some(1.23 + i as f64),
            ORIGIN_UNITS: Some("V".to_string()),
            TEST_ORDER: Some(i as i64),
            ALARM_ID: Some(format!("ALARM_{}", i)),
            OPT_FLG: Some("Y".to_string()),
            RES_SCAL: Some(6),
            NUM_TEST: Some(1),
            LLM_SCAL: Some(6),
            HLM_SCAL: Some(6),
            LO_LIMIT: Some(0.9),
            HI_LIMIT: Some(1.5),
            ORIGIN_HI_LIMIT: Some(1.5),
            ORIGIN_LO_LIMIT: Some(0.9),
            C_RESFMT: Some("%.6f".to_string()),
            C_LLMFMT: Some("%.6f".to_string()),
            C_HLMFMT: Some("%.6f".to_string()),
            LO_SPEC: Some(0.8),
            HI_SPEC: Some(1.6),
            HBIN_NUM: Some(1),
            SBIN_NUM: Some(1),
            SBIN_PF: Some("P".to_string()),
            SBIN_NAM: Some("PASS".to_string()),
            HBIN_PF: Some("P".to_string()),
            HBIN_NAM: Some("PASS".to_string()),
            HBIN: Some("1".to_string()),
            SBIN: Some("1".to_string()),
            TEST_HEAD: Some(1),
            PART_FLG: Some("P".to_string()),
            PART_ID: Some(format!("PART_{}", i)),
            C_PART_ID: Some(i as i64),
            ECID: Some(format!("ECID_{}", i)),
            ECID_EXT: Some(format!("ECID_EXT_{}", i)),
            ECID_EXTRA: Some(ecid_extra),
            IS_STANDARD_ECID: Some(1),
            X_COORD: Some((i % 100) as i32),
            Y_COORD: Some((i / 100) as i32),
            DIE_X: Some((i % 100) as i32),
            DIE_Y: Some((i / 100) as i32),
            TEST_TIME: Some(1000000 + i as i64),
            PART_TXT: Some(format!("PART_TXT_{}", i)),
            PART_FIX: Some(format!("PART_FIX_{}", i)),
            SITE: Some(1),
            TOUCH_DOWN_ID: Some(1),
            WAFER_LOT_ID: Some("WAFER_LOT_001".to_string()),
            WAFER_ID: Some("WAFER_001".to_string()),
            WAFER_NO: Some("25".to_string()),
            RETICLE_T_X: Some(0),
            RETICLE_T_Y: Some(0),
            RETICLE_X: Some(0),
            RETICLE_Y: Some(0),
            SITE_ID: Some("SITE_1".to_string()),
            VECT_NAM: Some(format!("VECT_{}", i)),
            TIME_SET: Some(format!("TIME_SET_{}", i)),
            NUM_FAIL: Some(0),
            FAIL_PIN: Some("".to_string()),
            CYCL_CNT: Some(1),
            REPT_CNT: Some(1),
            LONG_ATTRIBUTE_SET: Some(long_attribute_set),
            STRING_ATTRIBUTE_SET: Some(string_attribute_set),
            FLOAT_ATTRIBUTE_SET: Some(float_attribute_set),
            UID: Some(format!("UID_{}", i)),
            TEXT_DAT: Some(format!("TEXT_DAT_{}", i)),
            CREATE_HOUR_KEY: Some("2024010100".to_string()),
            CREATE_DAY_KEY: Some("20240101".to_string()),
            CREATE_TIME: Utc::now().timestamp_millis(),
            EFUSE_EXTRA: Some(efuse_extra),
            CHIP_ID: Some(format!("CHIP_{}", i)),
        });
    }
    
    data
}

async fn test_chunk_parquet_write(test_data: &Vec<SubTestItemDetail>) -> Result<(), Box<dyn std::error::Error>> {
    println!("开始测试chunk parquet写入功能...");
    
    // 创建测试目录
    let test_dir = "./test_chunk_output";
    std::fs::create_dir_all(test_dir)?;
    
    // 按chunk异步写入多个parquet文件
    let chunk_size = 500; // 每个文件500条记录
    let chunks: Vec<_> = test_data.chunks(chunk_size).collect();
    let total_chunks = chunks.len();
    
    println!("开始异步写入 {} 个parquet文件，每个文件最多 {} 条记录", total_chunks, chunk_size);
    
    // 创建异步任务列表
    let mut tasks = Vec::new();
    
    for (index, chunk) in chunks.into_iter().enumerate() {
        let chunk_data = chunk.to_vec();
        let path = test_dir.to_string();
        let hdfs_config = HdfsConfig::default();
        
        // 生成文件名：part-0001.parquet, part-0002.parquet, ...
        let file_name = format!("part-{:04}.parquet", index + 1);
        
        // 创建异步任务
        let task = tokio::spawn(async move {
            println!("开始写入文件: {}/{}", path, file_name);
            let result = write_parquet(&path, &file_name, &chunk_data, None).await; // 使用本地文件系统
            match &result {
                Ok(_) => println!("成功写入文件: {}/{} ({} 条记录)", path, file_name, chunk_data.len()),
                Err(e) => eprintln!("写入文件失败: {}/{}, 错误: {}", path, file_name, e),
            }
            result.map(|_| (file_name, chunk_data.len()))
        });
        
        tasks.push(task);
    }
    
    // 等待所有任务完成
    let results = futures::future::join_all(tasks).await;
    
    // 检查结果并统计
    let mut success_count = 0;
    let mut total_records = 0;
    let mut failed_files = Vec::new();
    
    for (index, result) in results.into_iter().enumerate() {
        match result {
            Ok(Ok((file_name, record_count))) => {
                success_count += 1;
                total_records += record_count;
                println!("文件 {} 写入成功，记录数: {}", file_name, record_count);
            },
            Ok(Err(e)) => {
                let file_name = format!("part-{:04}.parquet", index + 1);
                failed_files.push(file_name.clone());
                eprintln!("文件 {} 写入失败: {}", file_name, e);
            },
            Err(e) => {
                let file_name = format!("part-{:04}.parquet", index + 1);
                failed_files.push(file_name.clone());
                eprintln!("任务 {} 执行失败: {}", file_name, e);
            }
        }
    }
    
    if !failed_files.is_empty() {
        return Err(format!("部分文件写入失败: {:?}", failed_files).into());
    }
    
    println!("所有parquet文件写入完成！成功写入 {} 个文件，总记录数: {}", success_count, total_records);
    
    // 验证文件是否存在
    for i in 1..=total_chunks {
        let file_path = format!("{}/part-{:04}.parquet", test_dir, i);
        if std::path::Path::new(&file_path).exists() {
            println!("✓ 文件存在: {}", file_path);
        } else {
            eprintln!("✗ 文件不存在: {}", file_path);
        }
    }
    
    println!("chunk parquet写入测试完成！");
    Ok(())
}
