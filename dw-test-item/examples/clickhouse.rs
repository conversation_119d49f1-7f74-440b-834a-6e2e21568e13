use chrono::{Local, Utc};
use ck_provider::{Ck<PERSON>onfig, <PERSON><PERSON><PERSON><PERSON><PERSON>, CkProviderImpl};
use common::dto::dwd::{die_detail_row::DieDetailRow, test_item_detail_row::TestItemDetailRow};
use common::utils::date::Format;
use common::utils::decimal::IntoDecimal38_18;
use dw_test_item::config::DwTestItemConfig;
use std::time::Duration;

use rust_decimal::Decimal;

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    // 读取配置文件

    let config = DwTestItemConfig::get_config()?;

    // 配置ClickHouse连接
    let dwd_config = CkConfig {
        url: format!("http://{}", config.ck_node_host),
        username: config.ck_username.clone(),
        password: config.ck_password.clone(),
        database: config.dwd_db_name.clone(),
        timeout: Duration::from_secs(30),
        batch_size: 1000,
        compression: true,
    };

    // 创建ClickHouse Provider
    let provider = CkProviderImpl::new(dwd_config);

    let test_config = CkConfig {
        url: format!("http://{}", config.ck_node_host),
        username: config.ck_username,
        password: config.ck_password,
        database: config.dwd_db_name,
        timeout: Duration::from_secs(30),
        batch_size: 1000,
        compression: true,
    };

    let test_provider = CkProviderImpl::new(test_config);

    // 执行基本查询
    println!("执行查询...");
    let count_query = "SELECT COUNT(*) FROM dwd.dwd_die_detail_local";
    if let Some(count) = provider.count(count_query).await? {
        println!("数据库记录总数: {}", count);
    }

    // 执行简单的查询，只选择几个字段
    println!("执行简单查询...");
    let simple_query = "select * from dwd.dwd_die_detail_cluster where DIE_HEIGHT = 53.937008000000000000 limit 5";
    let mut results: Vec<DieDetailRow> = provider.query(simple_query).await?;

    println!("查询结果:{:?}", results[0].DIE_HEIGHT);

    // 修改die_height为加10以后的结果
    for result in results.iter_mut() {
        let die_height = "666.12345678901234567890".parse::<Decimal>()?;
        // 使用自定义trait将Decimal转换为Decimal38_18
        result.DIE_HEIGHT = Some(die_height.into_decimal38_18());
    }

    // 执行写入到test.dwd_die_detail_local
    test_provider.insert("test.dwd_die_detail_local", &results).await?;

    // 查询test.dwd_die_detail_local
    let query = "select * from dwd.dwd_test_item_detail_local limit 1";
    let mut results: Vec<TestItemDetailRow> = test_provider.query(query).await?;
    for result in results.iter_mut() {
        println!("查询结果:{:?}", result);
        // 下面两个值最后保存到clickhouse中是一样的
        result.CREATE_TIME = Local::now().into();
        result.UPLOAD_TIME = Utc::now();
        println!("upload_time:{:?}", result.UPLOAD_TIME.into_format());
    }

    test_provider.insert("test.dwd_test_item_detail_local", &results).await?;

    Ok(())
}
