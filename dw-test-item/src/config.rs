use anyhow::{Context, Result};
use ck_provider::CkConfig;
use java_properties::read;
use mysql_provider::MySqlConfig;
use rand::prelude::*;
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::path::Path;
use std::time::Duration;
use std::{fs::File, io::BufReader};

// 常量定义
const COMMA: &str = ",";

const DEFAULT_CONFIG_PATH: &str = "dataware-dw-test-item-3.4.5.properties";

/// DwTestItemConfig结构体，对应Java的DwTestItemProperties类
#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct DwTestItemConfig {
    // stdf相关路径
    pub cp_result_dir: String,
    pub ft_result_dir: String,
    pub cp_die_detail_result_dir: String,
    pub die_detail_result_dir: String,
    pub cp_test_item_detail_result_dir: String,
    pub test_item_detail_result_dir: String,
    pub all_die_detail_path: String,
    pub all_test_item_detail_path: String,

    // dim路径模板
    pub cp_dim_result_dir_template: String,
    pub ft_dim_result_dir_template: String,

    // dws路径模板
    pub cp_dws_result_dir_template: String,
    pub ft_dws_result_dir_template: String,

    // bitmem相关路径
    pub cp_die_bitmem_detail_result_dir: String,
    pub die_bitmem_detail_result_dir: String,
    pub cp_test_item_bitmem_detail_result_dir: String,
    pub test_item_bitmem_detail_result_dir: String,

    // 分区设置
    pub test_item_detail_result_partition: i32,
    pub test_item_bitmem_detail_result_partition: i32,
    pub zstd_max_partition_bytes: String,
    pub dim_result_partition: i32,
    pub dws_result_partition: i32,
    pub lot_bucket_num: i32,

    // 单位标准化
    pub standard_units: String,

    // 数据库名称
    pub dwd_db_name: String,
    pub dim_db_name: String,
    pub dws_db_name: String,
    pub ads_db_name: String,
    pub ods_db_name: String,
    pub meta_db_name: String,

    // ClickHouse配置
    pub ck_protocol: String,
    pub ck_address: String,
    pub ck_username: String,
    pub ck_password: String,
    pub ck_batch_size: String,
    pub ck_cluster: String,
    pub dim_num_partitions: String,
    pub index_num_partition: String,
    pub ck_fetch_size: String,
    pub ck_node_host: String,
    pub ck_node_user: String,
    pub ck_node_password: String,
    pub parquet_block_size: i32,
    pub total_multi_if_limit: i32,

    // MySQL配置
    pub address: String,
    pub driver: String,
    pub username: String,
    pub password: String,
    pub fetch_size: String,
    pub onedatadbname: String,

    // Kafka配置
    pub bootstrap_servers: String,

    // 其他设置
    pub need_multiply_scale_customer: String,
    pub insert_cluster_table: bool,
    pub cp_test_num_clear_flag: String,
    pub ft_test_num_clear_flag: String,

    // Redis配置
    pub redis_address: String,
    pub redis_password: String,

    // YMS配置
    pub yms_test_item_site_bin_partition: String,
    pub yms_test_item_bin_partition: String,
    pub yms_test_item_site_partition: String,
    pub yms_test_item_program_partition: String,
    pub yms_die_detail_result_partition: String,
    pub yms_wafer_bin_result_partition: String,

    // 需要清洗的客户数据
    pub need_clear_invalid_data_customer: String,
}

impl DwTestItemConfig {
    /// 从指定路径加载配置
    pub fn from_file<P: AsRef<Path>>(path: P) -> Result<Self> {
        let file = File::open(path).context("无法打开配置文件")?;
        let reader = BufReader::new(file);
        let props = read(reader).context("无法解析properties文件")?;

        Self::from_properties(props)
    }

    pub fn get_config() -> Result<Self> {
        let path = Path::new(DEFAULT_CONFIG_PATH);
        Self::from_file(path)
    }

    /// 从properties HashMap构建配置
    fn from_properties(props: HashMap<String, String>) -> Result<Self> {
        let get_prop =
            |key: &str| -> Result<String> { props.get(key).cloned().context(format!("配置项 {} 不存在", key)) };

        let get_bool_prop = |key: &str| -> Result<bool> {
            let value = get_prop(key)?;
            value.parse::<bool>().context(format!("配置项 {} 不是有效的布尔值", key))
        };

        let get_i32_prop = |key: &str| -> Result<i32> {
            let value = get_prop(key)?;
            value.parse::<i32>().context(format!("配置项 {} 不是有效的整数", key))
        };

        Ok(DwTestItemConfig {
            // stdf相关路径
            cp_result_dir: get_prop("task.onedata.dataware.ods.cpResultDir")?,
            ft_result_dir: get_prop("task.onedata.dataware.ods.ftResultDir")?,
            cp_die_detail_result_dir: get_prop("task.onedata.dataware.dwd.cpDieDetailResultDir")?,
            die_detail_result_dir: get_prop("task.onedata.dataware.dwd.dieDetailResultDir")?,
            cp_test_item_detail_result_dir: get_prop("task.onedata.dataware.dwd.cpTestItemDetailResultDir")?,
            test_item_detail_result_dir: get_prop("task.onedata.dataware.dwd.testItemDetailResultDir")?,
            all_die_detail_path: get_prop("task.onedata.dataware.dim.allDieDetailPath")?,
            all_test_item_detail_path: get_prop("task.onedata.dataware.dim.allTestItemDetailPath")?,

            // dim路径模板
            cp_dim_result_dir_template: get_prop("task.onedata.dataware.dim.cpDimResultDirTemplate")?,
            ft_dim_result_dir_template: get_prop("task.onedata.dataware.dim.ftDimResultDirTemplate")?,

            // dws路径模板
            cp_dws_result_dir_template: get_prop("task.onedata.dataware.dws.cpDwsResultDirTemplate")?,
            ft_dws_result_dir_template: get_prop("task.onedata.dataware.dws.ftDwsResultDirTemplate")?,

            // bitmem相关路径
            cp_die_bitmem_detail_result_dir: get_prop("task.onedata.dataware.dwd.cpDieBitmemDetailResultDir")?,
            die_bitmem_detail_result_dir: get_prop("task.onedata.dataware.dwd.dieBitmemDetailResultDir")?,
            cp_test_item_bitmem_detail_result_dir: get_prop(
                "task.onedata.dataware.dwd.cpTestItemBitmemDetailResultDir",
            )?,
            test_item_bitmem_detail_result_dir: get_prop("task.onedata.dataware.dwd.testItemBitmemDetailResultDir")?,

            // 分区设置
            test_item_detail_result_partition: get_i32_prop("task.onedata.dataware.dwd.testItemDetailResultPartition")?,
            test_item_bitmem_detail_result_partition: get_i32_prop(
                "task.onedata.dataware.dwd.testItemBitmemDetailResultPartition",
            )?,
            zstd_max_partition_bytes: get_prop("task.onedata.dataware.dwd.zstdMaxPartitionBytes")?,
            dim_result_partition: get_i32_prop("task.onedata.dataware.dim.dimResultPartition")?,
            dws_result_partition: get_i32_prop("task.onedata.dataware.dws.dwsResultPartition")?,
            lot_bucket_num: get_i32_prop("task.onedata.dataware.dwd.lotBucketNum")?,

            // 单位标准化
            standard_units: get_prop("task.onedata.dataware.dwd.standardUnits")?,

            // 数据库名称
            dwd_db_name: get_prop("task.onedata.dataware.dwd.dwdDbName")?,
            dim_db_name: get_prop("task.onedata.dataware.dim.dimDbName")?,
            dws_db_name: get_prop("task.onedata.dataware.dws.dwsDbName")?,
            ads_db_name: get_prop("task.onedata.dataware.ads.adsDbName")?,
            ods_db_name: get_prop("task.onedata.dataware.ods.odsDbName")?,
            meta_db_name: get_prop("task.onedata.dataware.meta.metaDbName")?,

            // ClickHouse配置
            ck_protocol: get_prop("data.clickhouse.ckProtocol")?,
            ck_address: get_prop("data.clickhouse.ckAddress")?,
            ck_username: get_prop("data.clickhouse.ckUsername")?,
            ck_password: get_prop("data.clickhouse.ckPassword")?,
            ck_batch_size: get_prop("data.clickhouse.ckBatchSize")?,
            ck_cluster: get_prop("data.clickhouse.ckCluster")?,
            dim_num_partitions: get_prop("data.clickhouse.dimNumPartitions")?,
            index_num_partition: get_prop("data.clickhouse.indexNumPartition")?,
            ck_fetch_size: get_prop("data.clickhouse.ckFetchSize")?,
            ck_node_host: get_prop("data.clickhouse.ckNodeHost")?,
            ck_node_user: get_prop("data.clickhouse.ckNodeUser")?,
            ck_node_password: get_prop("data.clickhouse.ckNodePassword")?,
            parquet_block_size: get_i32_prop("data.clickhouse.parquetBlockSize")?,
            total_multi_if_limit: get_i32_prop("data.clickhouse.totalMultiIfLimit")?,

            // MySQL配置
            address: get_prop("data.mysql.address")?,
            driver: get_prop("data.mysql.driver")?,
            username: get_prop("data.mysql.username")?,
            password: get_prop("data.mysql.password")?,
            fetch_size: get_prop("data.mysql.fetchSize")?,
            onedatadbname: get_prop("data.mysql.onedatadbname")?,

            // Kafka配置
            bootstrap_servers: get_prop("kafka.bootstrapServers")?,

            // 其他设置
            need_multiply_scale_customer: get_prop("settings.needMultiplyScaleCustomer")?,
            insert_cluster_table: get_bool_prop("insertClusterTable")?,
            cp_test_num_clear_flag: get_prop("settings.cpTestNumClearFlag")?,
            ft_test_num_clear_flag: get_prop("settings.ftTestNumClearFlag")?,

            // Redis配置
            redis_address: get_prop("redisAddress")?,
            redis_password: get_prop("redisPassword")?,

            // YMS配置
            yms_test_item_site_bin_partition: get_prop("task.onedata.yms.ads.ymsTestItemSiteBinPartition")?,
            yms_test_item_bin_partition: get_prop("task.onedata.yms.ads.ymsTestItemBinPartition")?,
            yms_test_item_site_partition: get_prop("task.onedata.yms.ads.ymsTestItemSitePartition")?,
            yms_test_item_program_partition: get_prop("task.onedata.yms.ads.ymsTestItemProgramPartition")?,
            yms_die_detail_result_partition: get_prop("task.onedata.yms.dwd.ymsDieDetailResultPartition")?,
            yms_wafer_bin_result_partition: get_prop("task.onedata.yms.dws.ymsWaferBinResultPartition")?,

            // 需要清洗的客户数据
            need_clear_invalid_data_customer: get_prop("settings.needClearInvalidDataCustomer")?,
        })
    }

    /// 获取随机的ClickHouse地址
    pub fn get_ck_address(&self) -> String {
        let addresses: Vec<&str> = self.ck_address.split(COMMA).collect();
        let mut rng = rand::thread_rng();
        let index = rng.gen_range(0..addresses.len());
        addresses[index].to_string()
    }

    /// 获取ClickHouse地址列表
    pub fn get_ck_address_list(&self) -> Vec<String> {
        self.ck_node_host.split(COMMA).map(|s| format!("http://{}", s.trim())).collect()
    }

    /// 根据分区获取ClickHouse地址
    pub fn get_ck_address_for_partition(&self, partition: &str) -> String {
        let addresses: Vec<&str> = self.ck_address.split(COMMA).collect();
        let hash = partition.chars().fold(0, |acc, c| acc + (c as i32));
        let index = (hash.abs() as usize) % addresses.len();
        addresses[index].to_string()
    }

    /// 获取需要清洗的客户数据列表
    pub fn get_need_clear_invalid_data_customer_list(&self) -> Vec<String> {
        self.need_clear_invalid_data_customer
            .split(COMMA)
            .filter(|s| !s.is_empty())
            .map(String::from)
            .collect()
    }

    pub fn get_need_multiply_scale(&self, customer: &str) -> bool {
        self.need_multiply_scale_customer.contains(customer)
    }

    /// Determine if invalid data clearing is needed based on customer
    ///
    /// Corresponds to: getClearInvalidDataFlag method referenced in line 55
    pub fn get_clear_invalid_data_flag(&self, customer: &str) -> bool {
        self.need_clear_invalid_data_customer.contains(customer)
    }

    pub fn get_mysql_config(&self) -> MySqlConfig {
        // 从JDBC URL解析主机和端口
        // 格式: *******************************
        let url_parts: Vec<&str> = self.address.split("://").collect();
        let host_port_db: Vec<&str> = url_parts[1].split('/').collect();
        let host_port: Vec<&str> = host_port_db[0].split(':').collect();

        let host = host_port.get(0).unwrap().to_string();
        let port = host_port.get(1).unwrap().parse::<u16>().unwrap();
        let database = self.onedatadbname.clone();

        // 创建MySQL配置
        let mysql_config = MySqlConfig {
            host: host.clone(),
            port,
            username: self.username.clone(),
            password: self.password.clone(),
            database: database.clone(),
            max_connections: 10,
            min_connections: 1,
            connection_timeout: Duration::from_secs(5),
            max_lifetime: Duration::from_secs(30 * 60),
            idle_timeout: Duration::from_secs(10 * 60),
        };
        mysql_config
    }

    pub fn get_ck_config(&self, database: &str) -> CkConfig {
        CkConfig {
            url: self.pick_random_ck_node_host(),
            username: self.ck_username.clone(),
            password: self.ck_password.clone(),
            database: database.to_string(),
            timeout: Duration::from_secs(30),
            batch_size: 1000,
            compression: true,
        }
    }

    pub fn pick_random_ck_node_host(&self) -> String {
        let addresses: Vec<&str> = self.ck_node_host.split(COMMA).map(str::trim).collect();
        let mut rng = rand::thread_rng();
        let index = rng.gen_range(0..addresses.len());
        format!("http://{}", addresses[index].to_string())
    }
}
