use ck_provider::{write_to_ck_parallel, CkConfig, CkProvider, CkProviderError, CkProviderExt, CkProviderImpl};
use clickhouse::Row;
use serde::{Deserialize, Serialize};
use std::time::Duration;

// 定义测试用的行结构
#[derive(Row, Serialize, Deserialize, Debug, Clone, PartialEq)]
struct TestUser {
    id: u32,
    name: String,
    age: u8,
}

// 设置测试环境
fn setup_test_config() -> CkConfig {
    CkConfig {
        url: "http://localhost:8123".to_string(),
        username: "admin".to_string(),
        password: "123456".to_string(),
        database: "test".to_string(),
        timeout: Duration::from_secs(30),
        batch_size: 100,
        compression: false,
    }
}

// 创建测试表并返回provider
async fn setup_test_table(table_name: &str) -> Result<CkProviderImpl, CkProviderError> {
    let config = setup_test_config();
    let provider = CkProviderImpl::new(config);

    // 确保表不存在
    provider.execute(&format!("DROP TABLE IF EXISTS {}", table_name)).await?;

    // 创建测试表
    provider
        .execute(&format!("CREATE TABLE {} (id UInt32, name String, age UInt8) ENGINE = Memory", table_name))
        .await?;

    Ok(provider)
}

// 清理测试表
async fn cleanup_test_table(provider: &CkProviderImpl, table_name: &str) -> Result<(), CkProviderError> {
    provider.execute(&format!("DROP TABLE IF EXISTS {}", table_name)).await
}

// 生成测试数据
fn generate_test_users(count: u32) -> Vec<TestUser> {
    (1..=count)
        .map(|i| TestUser { id: i, name: format!("用户{}", i), age: (20 + (i % 50)) as u8 })
        .collect()
}

#[cfg(feature = "run-tests")]
#[tokio::test]
async fn test_insert_and_query() {
    let table_name = "test_insert_query";
    let provider = setup_test_table(table_name).await.unwrap();

    // 准备测试数据
    let users = generate_test_users(5);

    // 测试插入
    let insert_result = provider.insert(table_name, &users).await;
    assert!(insert_result.is_ok(), "插入应该成功");

    // 测试查询
    let query_result: Result<Vec<TestUser>, _> =
        provider.query(&format!("SELECT * FROM {} ORDER BY id", table_name)).await;

    assert!(query_result.is_ok(), "查询应该成功");
    let fetched_users = query_result.unwrap();
    assert_eq!(fetched_users.len(), 5, "应该返回5条记录");
    assert_eq!(fetched_users, users, "返回的数据应与插入的数据一致");

    // 清理
    cleanup_test_table(&provider, table_name).await.unwrap();
}

#[cfg(feature = "run-tests")]
#[tokio::test]
async fn test_count() {
    let table_name = "test_count";
    let provider = setup_test_table(table_name).await.unwrap();

    // 插入测试数据
    let users = generate_test_users(10);
    provider.insert(table_name, &users).await.unwrap();

    // 测试计数
    let count_result = provider.count(&format!("SELECT COUNT(*) FROM {}", table_name)).await;

    assert!(count_result.is_ok(), "计数查询应该成功");
    assert_eq!(count_result.unwrap(), Some(10), "应该返回10条记录的计数");

    // 测试条件计数
    let filtered_count = provider
        .count(&format!("SELECT COUNT(*) FROM {} WHERE age > 30", table_name))
        .await;

    assert!(filtered_count.is_ok(), "条件计数查询应该成功");

    // 清理
    cleanup_test_table(&provider, table_name).await.unwrap();
}

#[cfg(feature = "run-tests")]
#[tokio::test]
async fn test_execute() {
    let table_name = "test_execute";
    let provider = setup_test_table(table_name).await.unwrap();

    // 测试执行DDL
    let alter_result = provider
        .execute(&format!("ALTER TABLE {} ADD COLUMN email String", table_name))
        .await;

    assert!(alter_result.is_ok(), "执行ALTER TABLE应该成功");

    // 验证列已添加
    let query_result = provider
        .execute(&format!(
            "INSERT INTO {} (id, name, age, email) VALUES (1, 'Test', 25, '<EMAIL>')",
            table_name
        ))
        .await;

    assert!(query_result.is_ok(), "插入新列应该成功");

    // 清理
    cleanup_test_table(&provider, table_name).await.unwrap();
}

#[cfg(feature = "run-tests")]
#[tokio::test]
async fn test_query_iter() {
    let table_name = "test_query_iter";
    let provider = setup_test_table(table_name).await.unwrap();

    // 插入测试数据
    let users = generate_test_users(5);
    provider.insert(table_name, &users).await.unwrap();

    // 测试迭代器查询
    let mut iter = provider
        .query_iter::<TestUser>(&format!("SELECT * FROM {} ORDER BY id", table_name))
        .await
        .unwrap();

    // 验证迭代结果
    let mut count = 0;
    while let Ok(Some(user)) = iter.next().await {
        count += 1;
        assert_eq!(user.id, count as u32, "应该按ID顺序返回记录");
    }

    assert_eq!(count, 5, "迭代器应该返回5条记录");

    // 清理
    cleanup_test_table(&provider, table_name).await.unwrap();
}

#[cfg(feature = "run-tests")]
#[tokio::test]
async fn test_parallel_write() {
    let table_name = "™";
    let provider = setup_test_table(table_name).await.unwrap();

    // 生成大量测试数据
    let users = generate_test_users(10000);

    // 测试并行写入
    let result = write_to_ck_parallel(&provider, table_name, &users, 8).await;
    assert!(result.is_ok(), "并行写入应该成功");

    // 验证写入结果
    let count = provider.count(&format!("SELECT COUNT(*) FROM {}", table_name)).await.unwrap();

    assert_eq!(count, Some(10000), "应该写入10000条记录");

    // 测试单线程写入
    let more_users = generate_test_users(500)
        .into_iter()
        .map(|mut u| {
            u.id += 1000; // 避免ID冲突
            u
        })
        .collect::<Vec<_>>();

    let result = write_to_ck_parallel(&provider, table_name, &more_users, 1).await;
    assert!(result.is_ok(), "单线程写入应该成功");

    // 验证写入结果
    let count = provider.count(&format!("SELECT COUNT(*) FROM {}", table_name)).await.unwrap();

    assert_eq!(count, Some(10500), "应该总共有10500条记录");

    // 清理
    // cleanup_test_table(&provider, table_name).await.unwrap();
}

#[cfg(feature = "run-tests")]
#[tokio::test]
async fn test_error_handling() {
    let config = setup_test_config();
    let provider = CkProviderImpl::new(config);

    // 测试无效的SQL语法
    let invalid_sql = "SELECT * FROM non_existent_table WHERE xyz";
    let result = provider.execute(invalid_sql).await;
    assert!(result.is_err(), "执行无效SQL应该返回错误");

    // 测试无效的表名
    let result = provider.insert("non_existent_table", &generate_test_users(1)).await;
    assert!(result.is_err(), "插入不存在的表应该返回错误");

    // 测试无效的查询
    let result: Result<Vec<TestUser>, _> = provider.query("SELECT * FROM non_existent_table").await;
    assert!(result.is_err(), "查询不存在的表应该返回错误");
}
