use anyhow::{anyhow, Result};
use async_trait::async_trait;
use clickhouse::{Client, Row};
use serde::{de::DeserializeOwned, Deserialize, Serialize};
use std::time::{Duration, Instant};
use thiserror::Error;

// 定义ClickHouse连接错误
#[derive(Debug, Error)]
pub enum CkProviderError {
    #[error("数据库连接错误: {0}")]
    ConnectionError(#[from] clickhouse::error::Error),
    #[error("SQL执行错误: {0}")]
    ExecutionError(String),
    #[error("数据解析错误: {0}")]
    ParseError(String),
}

// 定义ClickHouse提供程序配置
#[derive(Clone, Debug)]
pub struct CkConfig {
    pub url: String,
    pub username: String,
    pub password: String,
    pub database: String,
    pub timeout: Duration,
    pub batch_size: usize,
    pub compression: bool,
}

impl Default for CkConfig {
    fn default() -> Self {
        Self {
            url: "http://localhost:8123".to_string(),
            username: "default".to_string(),
            password: "".to_string(),
            database: "default".to_string(),
            timeout: Duration::from_secs(30),
            batch_size: 1000,
            compression: true,
        }
    }
}

// 定义ClickHouse提供程序接口
#[async_trait]
pub trait CkProvider {
    // 执行查询并返回结构体集合
    async fn query<T>(&self, query: &str) -> Result<Vec<T>, CkProviderError>
    where
        T: Row + DeserializeOwned + Send + 'static;

    // 执行插入操作
    async fn insert<T>(&self, table: &str, data: &[T]) -> Result<(), CkProviderError>
    where
        T: Row + Serialize + Send + Sync + 'static;

    // 执行SQL语句
    async fn execute(&self, query: &str) -> Result<(), CkProviderError>;

    // 执行计数查询
    async fn count(&self, query: &str) -> Result<Option<i64>, CkProviderError>;

    // 获取客户端引用
    fn get_client(&self) -> &Client;
}

// ClickHouse提供程序实现
pub struct CkProviderImpl {
    client: Client,
    config: CkConfig,
}

impl CkProviderImpl {
    // 创建新的ClickHouse客户端
    pub fn new(config: CkConfig) -> Self {
        // TODO fix
        let url = config.url.replace("jdbc:clickhouse://", "http://");
        log::info!("clickhouse url: {}", url);
        let mut client = Client::default()
            .with_url(&url)
            .with_user(&config.username)
            .with_password(&config.password)
            .with_database(&config.database)
            .with_option("connect_timeout", config.timeout.as_secs().to_string());
        if config.compression {
            client = client.with_compression(clickhouse::Compression::Lz4);
        }

        Self { client, config }
    }
}

#[async_trait]
impl CkProvider for CkProviderImpl {
    // 执行查询并返回结构体集合
    async fn query<T>(&self, query: &str) -> Result<Vec<T>, CkProviderError>
    where
        T: Row + DeserializeOwned + Send + 'static,
    {
        log::info!("读取ck开始, {}", query);
        let start = Instant::now();

        let result = self.client.query(query).fetch_all::<T>().await.map_err(|e| {
            let err_str = e.to_string();
            log::error!("读取ck失败: {}", anyhow!(e));
            CkProviderError::ExecutionError(err_str)
        })?;

        log::info!("读取ck完成，耗时：{:?}, dataSize: {}", start.elapsed(), result.len());
        Ok(result)
    }

    // 执行插入操作
    async fn insert<T>(&self, table: &str, data: &[T]) -> Result<(), CkProviderError>
    where
        T: Row + Serialize + Send + Sync + 'static,
    {
        if data.is_empty() {
            return Ok(());
        }

        log::info!("数据写ck开始, dataSize: {}", data.len());
        let start = Instant::now();

        // 创建insert语句
        let insert_result = self.client.insert(table);
        if let Err(e) = &insert_result {
            return Err(CkProviderError::ExecutionError(e.to_string()));
        }
        let mut insert = insert_result.unwrap();

        // 分批写入数据
        for chunk in data.chunks(self.config.batch_size) {
            for item in chunk {
                insert
                    .write(item)
                    .await
                    .map_err(|e| CkProviderError::ExecutionError(e.to_string()))?;
            }
        }

        // 完成插入
        insert.end().await.map_err(|e| CkProviderError::ExecutionError(e.to_string()))?;

        log::info!("数据写ck完成，耗时：{:?}", start.elapsed());
        Ok(())
    }

    // 执行SQL语句
    async fn execute(&self, query: &str) -> Result<(), CkProviderError> {
        log::info!("执行ck开始 {}", query);
        let start = Instant::now();

        self.client.query(query).execute().await.map_err(|e| {
            let err_str = e.to_string();
            let msg = format!("{:#}", anyhow!(e));
            log::error!("{}", msg);
            CkProviderError::ExecutionError(err_str)
        })?;

        log::info!("执行ck完成，耗时：{:?}", start.elapsed());
        Ok(())
    }

    // 执行计数查询
    async fn count(&self, query: &str) -> Result<Option<i64>, CkProviderError> {
        log::info!("读取ck开始, {}", query);
        let start = Instant::now();
        #[derive(Row, Deserialize, Debug)]
        struct Count {
            #[serde(rename = "")]
            count: i64,
        }

        let result = self
            .client
            .query(query)
            .fetch_all::<Count>()
            .await
            .map_err(|e| CkProviderError::ExecutionError(e.to_string()))?;

        let count = if result.is_empty() { None } else { Some(result[0].count) };

        log::info!("读取ck完成，耗时：{:?}, count = {:?}", start.elapsed(), count);
        Ok(count)
    }

    // 获取客户端引用
    fn get_client(&self) -> &Client {
        &self.client
    }
}

// ClickHouse查询的行迭代器
pub struct CkRowIterator<T>
where
    T: Row + DeserializeOwned + Send + 'static,
{
    items: Vec<Option<T>>,
    index: usize,
}

impl<T> CkRowIterator<T>
where
    T: Row + DeserializeOwned + Send + 'static,
{
    pub fn new(items: Vec<T>) -> Self {
        // 将每个元素包装为Option<T>
        let items = items.into_iter().map(Some).collect();
        Self { items, index: 0 }
    }

    pub async fn next(&mut self) -> Result<Option<T>, CkProviderError> {
        if self.index < self.items.len() {
            let item = self.items[self.index].take();
            self.index += 1;
            Ok(item)
        } else {
            Ok(None)
        }
    }
}

// 扩展CkProvider接口，增加迭代器查询方法
#[async_trait]
pub trait CkProviderExt: CkProvider {
    // 使用迭代器模式查询数据
    async fn query_iter<T>(&self, query: &str) -> Result<CkRowIterator<T>, CkProviderError>
    where
        T: Row + DeserializeOwned + Send + 'static;
}

#[async_trait]
impl CkProviderExt for CkProviderImpl {
    async fn query_iter<T>(&self, query: &str) -> Result<CkRowIterator<T>, CkProviderError>
    where
        T: Row + DeserializeOwned + Send + 'static,
    {
        log::info!("迭代读取ck开始, {}", query);
        let items = self.query::<T>(query).await?;
        Ok(CkRowIterator::new(items))
    }
}

// 实现并行写入功能，类似于CkProvider.scala中的splitList和writeToCk
pub async fn write_to_ck_parallel<T>(
    provider: &impl CkProvider,
    table: &str,
    data: &[T],
    parallelism: usize,
) -> Result<(), CkProviderError>
where
    T: Row + Serialize + Send + Sync + Clone + 'static,
{
    if data.is_empty() {
        return Ok(());
    }

    log::info!("数据并行写ck开始, dataSize: {}", data.len());
    let start = Instant::now();

    if parallelism <= 1 {
        provider.insert(table, data).await?;
    } else {
        // 将数据分成parallelism份
        let chunk_size = (data.len() + parallelism - 1) / parallelism;
        let mut tasks = Vec::with_capacity(parallelism);

        for chunk in data.chunks(chunk_size) {
            let chunk_data = chunk.to_vec();
            let client = provider.get_client().clone();
            let table_name = table.to_string();

            // 创建异步任务
            let task = tokio::spawn(async move {
                // 创建insert语句
                let insert_result = client.insert(&table_name);
                if let Err(e) = &insert_result {
                    return Err(CkProviderError::ExecutionError(e.to_string()));
                }
                let mut insert = insert_result.unwrap();

                for item in &chunk_data {
                    insert
                        .write(item)
                        .await
                        .map_err(|e| CkProviderError::ExecutionError(e.to_string()))?;
                }

                insert.end().await.map_err(|e| CkProviderError::ExecutionError(e.to_string()))?;

                Ok::<_, CkProviderError>(())
            });

            tasks.push(task);
        }

        // 等待所有任务完成
        for task in tasks {
            match task.await {
                Ok(result) => result?,
                Err(e) => return Err(CkProviderError::ExecutionError(e.to_string())),
            }
        }
    }

    log::info!("数据并行写ck完成，耗时：{:?}", start.elapsed());
    Ok(())
}
